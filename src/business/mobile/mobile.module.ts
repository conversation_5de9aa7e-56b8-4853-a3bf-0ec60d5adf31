import { Module } from '@nestjs/common';
import { MobileAuthModule } from './auth/auth.module';
import { MobileOrdersModule } from './orders/orders.module';
import { MobileTimeClockModule } from './timeclock/timeclock.module';
import { MobileVehicleModule } from './vehicle/vehicle.module';
import { MobileDriverModule } from './driver/driver.module';

@Module({
  imports: [
    MobileAuthModule,
    MobileOrdersModule,
    MobileTimeClockModule,
    MobileVehicleModule,
    MobileDriverModule,
  ],
  exports: [
    MobileAuthModule,
    MobileOrdersModule,
    MobileTimeClockModule,
    MobileVehicleModule,
    MobileDriverModule,
  ],
})
export class MobileModule {}
