import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DriverService } from '../../user/drivers/driver.service';
import { MobileOrdersService } from '../orders/services/mobile-orders.service';
import { OrderDetailsResponseDto } from '../orders/dto/order-details-response.dto';

@ApiTags('Mobile - Driver')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller({
  path: 'mobile/driver',
  version: '1',
})
export class MobileDriverController {
  constructor(
    private readonly driverService: DriverService,
    private readonly ordersService: MobileOrdersService,
  ) {}

  @Get('drivers')
  @ApiOperation({ summary: 'Get all drivers for the tenant' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of drivers retrieved successfully',
  })
  async getDrivers(@Request() req: any) {
    const drivers = await this.driverService.getAllDrivers(req.user.sub);
    return drivers;
  }

  @Post('orders/:id/transfer')
  @ApiOperation({ summary: 'Transfer an order to another driver' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        newDriverId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440002',
        },
        reason: { type: 'string', example: 'Driver unavailable' },
      },
      required: ['newDriverId'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order transferred successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Order cannot be transferred' })
  async transferOrder(
    @Request() req: any,
    @Param('id') id: string,
    @Body('newDriverId') newDriverId: string,
    @Body('reason') reason: string,
  ) {
    const driverId = req.user.sub;
    await this.ordersService.transferOrder(
      driverId,
      id,
      newDriverId,
      reason || 'Driver requested transfer',
    );
    
    // Return updated order details
    const order = await this.ordersService.getDriverOrderById(driverId, id);
    return this.ordersService.mapToOrderDetails(order);
  }
}
